"""
Application Configuration Management

This module handles all application configuration using Apollo Configuration Center.
Only Apollo connection settings are loaded from environment variables,
all other configurations are retrieved dynamically from Apollo.
"""

import os
from functools import lru_cache
from typing import List, Optional, Any

from pydantic import Field
from pydantic_settings import BaseSettings

from app.core.apollo.apollo_client import ApolloClient


class ApolloConnectionSettings(BaseSettings):
    """
    Apollo connection settings loaded from environment variables.

    Only Apollo connection parameters are loaded from .env file,
    all other configurations are retrieved from Apollo.
    """

    # Apollo Configuration Center Connection
    APOLLO_APP_ID: str = Field(default="super-v5", description="Apollo app ID")
    APOLLO_CLUSTER: str = Field(default="default", description="Apollo cluster")
    APOLLO_NAMESPACE: str = Field(default="application", description="Apollo namespace")
    APOLLO_META_SERVER: str = Field(
        default="http://apollo-config-k8s.test.ywwl.com:80", description="Apollo meta server"
    )

    # Optional: Environment indicator
    ENVIRONMENT: str = Field(default="development", description="Environment")

    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


class Settings:
    """
    Application settings using Apollo Configuration Center.

    This class retrieves all configuration values from Apollo dynamically,
    except for Apollo connection settings which are loaded from environment variables.
    """

    def __init__(self):
        """Initialize settings with Apollo client."""
        # Load Apollo connection settings from environment
        self._apollo_conn = ApolloConnectionSettings()

        # Initialize Apollo client
        self._apollo_client = ApolloClient(
            app_id=self._apollo_conn.APOLLO_APP_ID,
            config_url=self._apollo_conn.APOLLO_META_SERVER,
            cluster=self._apollo_conn.APOLLO_CLUSTER
        )

    def _get_apollo_value(self, key: str, default: Any = None, value_type: type = str) -> Any:
        """Get value from Apollo with type conversion."""
        value = self._apollo_client.get_value(key, default, self._apollo_conn.APOLLO_NAMESPACE)

        # Type conversion
        if value is None:
            return default

        if value_type == bool:
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.lower() in ('true', '1', 'yes', 'on')
        elif value_type == int:
            return int(value) if value is not None else default
        elif value_type == float:
            return float(value) if value is not None else default
        elif value_type == list:
            if isinstance(value, list):
                return value
            if isinstance(value, str):
                # Try to parse as JSON list
                try:
                    import json
                    return json.loads(value)
                except:
                    # Fallback to comma-separated values
                    return [item.strip() for item in value.split(',') if item.strip()]

        return value

    # Environment
    @property
    def ENVIRONMENT(self) -> str:
        return self._apollo_conn.ENVIRONMENT

    # Application Configuration
    @property
    def APP_NAME(self) -> str:
        return self._get_apollo_value("APP_NAME", "super-v5")

    @property
    def APP_VERSION(self) -> str:
        return self._get_apollo_value("APP_VERSION", "0.1.0")

    @property
    def APP_DESCRIPTION(self) -> str:
        return self._get_apollo_value("APP_DESCRIPTION", "Super-V5 Enterprise LLM Application Backend Service Platform")

    @property
    def DEBUG(self) -> bool:
        return self._get_apollo_value("DEBUG", False, bool)

    # Server Configuration
    @property
    def HOST(self) -> str:
        return self._get_apollo_value("HOST", "0.0.0.0")

    @property
    def PORT(self) -> int:
        return self._get_apollo_value("PORT", 8000, int)

    @property
    def WORKERS(self) -> int:
        return self._get_apollo_value("WORKERS", 1, int)

    @property
    def RELOAD(self) -> bool:
        return self._get_apollo_value("RELOAD", True, bool)

    @property
    def ALLOWED_HOSTS(self) -> List[str]:
        return self._get_apollo_value("ALLOWED_HOSTS", ["*"], list)

    # Database Configuration
    @property
    def DATABASE_URL(self) -> str:
        return self._get_apollo_value("DATABASE_URL", "postgresql+asyncpg://username:password@localhost:5432/super_v5")

    @property
    def DATABASE_POOL_SIZE(self) -> int:
        return self._get_apollo_value("DATABASE_POOL_SIZE", 20, int)

    @property
    def DATABASE_MAX_OVERFLOW(self) -> int:
        return self._get_apollo_value("DATABASE_MAX_OVERFLOW", 30, int)

    @property
    def DATABASE_POOL_TIMEOUT(self) -> int:
        return self._get_apollo_value("DATABASE_POOL_TIMEOUT", 30, int)

    @property
    def DATABASE_POOL_RECYCLE(self) -> int:
        return self._get_apollo_value("DATABASE_POOL_RECYCLE", 3600, int)

    # Redis Configuration
    @property
    def REDIS_URL(self) -> str:
        return self._get_apollo_value("REDIS_URL", "redis://localhost:6379/0")

    @property
    def REDIS_PASSWORD(self) -> Optional[str]:
        return self._get_apollo_value("REDIS_PASSWORD", None)

    @property
    def REDIS_DB(self) -> int:
        return self._get_apollo_value("REDIS_DB", 0, int)

    @property
    def REDIS_MAX_CONNECTIONS(self) -> int:
        return self._get_apollo_value("REDIS_MAX_CONNECTIONS", 20, int)

    @property
    def REDIS_SOCKET_TIMEOUT(self) -> int:
        return self._get_apollo_value("REDIS_SOCKET_TIMEOUT", 5, int)

    @property
    def REDIS_SOCKET_CONNECT_TIMEOUT(self) -> int:
        return self._get_apollo_value("REDIS_SOCKET_CONNECT_TIMEOUT", 5, int)

    # Security Configuration
    @property
    def SECRET_KEY(self) -> str:
        return self._get_apollo_value("SECRET_KEY", "your-super-secret-key-change-this-in-production")

    @property
    def ALGORITHM(self) -> str:
        return self._get_apollo_value("ALGORITHM", "HS256")

    @property
    def ACCESS_TOKEN_EXPIRE_MINUTES(self) -> int:
        return self._get_apollo_value("ACCESS_TOKEN_EXPIRE_MINUTES", 30, int)

    @property
    def REFRESH_TOKEN_EXPIRE_DAYS(self) -> int:
        return self._get_apollo_value("REFRESH_TOKEN_EXPIRE_DAYS", 7, int)

    # CORS Configuration
    @property
    def CORS_ORIGINS(self) -> List[str]:
        return self._get_apollo_value("CORS_ORIGINS", ["http://localhost:3000", "http://localhost:8080"], list)

    @property
    def CORS_ALLOW_CREDENTIALS(self) -> bool:
        return self._get_apollo_value("CORS_ALLOW_CREDENTIALS", True, bool)

    @property
    def CORS_ALLOW_METHODS(self) -> List[str]:
        return self._get_apollo_value("CORS_ALLOW_METHODS", ["GET", "POST", "PUT", "DELETE", "OPTIONS"], list)

    @property
    def CORS_ALLOW_HEADERS(self) -> List[str]:
        return self._get_apollo_value("CORS_ALLOW_HEADERS", ["*"], list)

    # LLM Configuration
    @property
    def DEFAULT_LLM_PROVIDER(self) -> str:
        return self._get_apollo_value("DEFAULT_LLM_PROVIDER", "openai")

    @property
    def OPENAI_API_KEY(self) -> Optional[str]:
        return self._get_apollo_value("OPENAI_API_KEY", None)

    @property
    def OPENAI_BASE_URL(self) -> str:
        return self._get_apollo_value("OPENAI_BASE_URL", "https://api.openai.com/v1")

    @property
    def OPENAI_MODEL(self) -> str:
        return self._get_apollo_value("OPENAI_MODEL", "gpt-4-turbo-preview")

    @property
    def OPENAI_MAX_TOKENS(self) -> int:
        return self._get_apollo_value("OPENAI_MAX_TOKENS", 4096, int)

    @property
    def OPENAI_TEMPERATURE(self) -> float:
        return self._get_apollo_value("OPENAI_TEMPERATURE", 0.7, float)

    # Alternative LLM Providers
    @property
    def ANTHROPIC_API_KEY(self) -> Optional[str]:
        return self._get_apollo_value("ANTHROPIC_API_KEY", None)

    @property
    def AZURE_OPENAI_API_KEY(self) -> Optional[str]:
        return self._get_apollo_value("AZURE_OPENAI_API_KEY", None)

    @property
    def AZURE_OPENAI_ENDPOINT(self) -> Optional[str]:
        return self._get_apollo_value("AZURE_OPENAI_ENDPOINT", None)

    # LangGraph Configuration
    @property
    def LANGGRAPH_CHECKPOINT_BACKEND(self) -> str:
        return self._get_apollo_value("LANGGRAPH_CHECKPOINT_BACKEND", "redis")

    @property
    def LANGGRAPH_MAX_ITERATIONS(self) -> int:
        return self._get_apollo_value("LANGGRAPH_MAX_ITERATIONS", 50, int)

    @property
    def LANGGRAPH_TIMEOUT_SECONDS(self) -> int:
        return self._get_apollo_value("LANGGRAPH_TIMEOUT_SECONDS", 300, int)

    # Kafka Configuration
    @property
    def KAFKA_BOOTSTRAP_SERVERS(self) -> str:
        return self._get_apollo_value("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")

    @property
    def KAFKA_SECURITY_PROTOCOL(self) -> str:
        return self._get_apollo_value("KAFKA_SECURITY_PROTOCOL", "PLAINTEXT")

    @property
    def KAFKA_SASL_MECHANISM(self) -> str:
        return self._get_apollo_value("KAFKA_SASL_MECHANISM", "PLAIN")

    @property
    def KAFKA_SASL_USERNAME(self) -> Optional[str]:
        return self._get_apollo_value("KAFKA_SASL_USERNAME", None)

    @property
    def KAFKA_SASL_PASSWORD(self) -> Optional[str]:
        return self._get_apollo_value("KAFKA_SASL_PASSWORD", None)

    # Apollo Configuration Center (from connection settings)
    @property
    def APOLLO_APP_ID(self) -> str:
        return self._apollo_conn.APOLLO_APP_ID

    @property
    def APOLLO_CLUSTER(self) -> str:
        return self._apollo_conn.APOLLO_CLUSTER

    @property
    def APOLLO_NAMESPACE(self) -> str:
        return self._apollo_conn.APOLLO_NAMESPACE

    @property
    def APOLLO_META_SERVER(self) -> str:
        return self._apollo_conn.APOLLO_META_SERVER

    # Monitoring & Logging
    @property
    def LOG_LEVEL(self) -> str:
        return self._get_apollo_value("LOG_LEVEL", "INFO")

    @property
    def LOG_FORMAT(self) -> str:
        return self._get_apollo_value("LOG_FORMAT", "json")

    @property
    def ENABLE_METRICS(self) -> bool:
        return self._get_apollo_value("ENABLE_METRICS", True, bool)

    @property
    def METRICS_PORT(self) -> int:
        return self._get_apollo_value("METRICS_PORT", 9090, int)

    @property
    def SENTRY_DSN(self) -> Optional[str]:
        return self._get_apollo_value("SENTRY_DSN", None)

    # Rate Limiting
    @property
    def RATE_LIMIT_ENABLED(self) -> bool:
        return self._get_apollo_value("RATE_LIMIT_ENABLED", True, bool)

    @property
    def RATE_LIMIT_REQUESTS_PER_MINUTE(self) -> int:
        return self._get_apollo_value("RATE_LIMIT_REQUESTS_PER_MINUTE", 60, int)

    @property
    def RATE_LIMIT_BURST(self) -> int:
        return self._get_apollo_value("RATE_LIMIT_BURST", 10, int)

    # File Upload
    @property
    def MAX_UPLOAD_SIZE(self) -> int:
        return self._get_apollo_value("MAX_UPLOAD_SIZE", 10485760, int)

    @property
    def ALLOWED_FILE_TYPES(self) -> List[str]:
        return self._get_apollo_value("ALLOWED_FILE_TYPES", ["txt", "pdf", "docx", "md"], list)

    # Agent Configuration
    @property
    def DEFAULT_AGENT_TYPE(self) -> str:
        return self._get_apollo_value("DEFAULT_AGENT_TYPE", "v5_agent")

    @property
    def AGENT_TIMEOUT_SECONDS(self) -> int:
        return self._get_apollo_value("AGENT_TIMEOUT_SECONDS", 300, int)

    @property
    def MAX_CONCURRENT_AGENTS(self) -> int:
        return self._get_apollo_value("MAX_CONCURRENT_AGENTS", 10, int)

    # Tool Configuration
    @property
    def ENABLE_EXTERNAL_TOOLS(self) -> bool:
        return self._get_apollo_value("ENABLE_EXTERNAL_TOOLS", True, bool)

    @property
    def TOOL_TIMEOUT_SECONDS(self) -> int:
        return self._get_apollo_value("TOOL_TIMEOUT_SECONDS", 30, int)

    @property
    def MAX_TOOL_RETRIES(self) -> int:
        return self._get_apollo_value("MAX_TOOL_RETRIES", 3, int)

    # Development & Testing
    @property
    def TESTING(self) -> bool:
        return self._get_apollo_value("TESTING", False, bool)

    @property
    def TEST_DATABASE_URL(self) -> str:
        return self._get_apollo_value("TEST_DATABASE_URL", "postgresql+asyncpg://test:test@localhost:5432/super_v5_test")

    @property
    def PYTEST_TIMEOUT(self) -> int:
        return self._get_apollo_value("PYTEST_TIMEOUT", 30, int)


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with Apollo integration.

    Returns:
        Settings: Application settings instance that retrieves values from Apollo
    """
    return Settings()


class ApolloConfig:
    """
    Apollo Configuration Center integration.

    This class provides additional Apollo management functionality.
    """

    def __init__(self, settings: Settings):
        """Initialize Apollo configuration."""
        self.settings = settings
        self._apollo_client = settings._apollo_client

    async def start(self) -> None:
        """Start Apollo configuration monitoring."""
        # Apollo client starts monitoring automatically in __init__
        pass

    async def stop(self) -> None:
        """Stop Apollo configuration monitoring."""
        if hasattr(self._apollo_client, 'stop'):
            self._apollo_client.stop()

    def get_config(self, key: str, default: Optional[str] = None, namespace: str = "application") -> Optional[str]:
        """Get configuration value from Apollo."""
        return self._apollo_client.get_value(key, default, namespace)

    def update_config(self, key: str, value: str) -> None:
        """Update configuration value in Apollo."""
        # Note: Apollo client is typically read-only
        # Configuration updates should be done through Apollo admin interface
        raise NotImplementedError("Configuration updates should be done through Apollo admin interface")

    def refresh_config(self) -> None:
        """Force refresh configuration from Apollo."""
        # Apollo client handles automatic refresh through long polling
        pass
